"use client";

import { create } from "zustand";
import { persist, subscribeWithSelector } from "zustand/middleware";

// Individual node state interface
export interface NodeState {
  // Basic node information
  nodeId: string;
  nodeType: string;

  // Settings (what's currently in node.data)
  settings: {
    label: string;
    description: string;
    enabled: boolean;
    hasVerticalPorts: boolean;
    // Additional node-specific settings can be added here
    [key: string]: any;
  };

  // Input attributes with their values
  inputAttributes: Record<string, any>;

  // Output attributes with their values
  outputAttributes: Record<string, any>;

  // Edge relationships
  inputEdges: string[]; // Array of edge IDs that target this node
  outputEdges: string[]; // Array of edge IDs that source from this node

  // Metadata
  createdAt: number;
  updatedAt: number;
}

// Helper function to get default input fields for a node type
function getDefaultInputFields(nodeType: string): Record<string, any> {
  const inputFieldsMap: Record<string, string[]> = {
    start: [], // Start nodes typically don't have inputs
    agent: ["prompt", "context", "temperature"],
    knowledge: ["query", "max_results", "threshold"],
    condition: ["input_value", "condition_type", "comparison_value"],
    function: ["function_name", "parameters", "timeout"],
    response: ["response_data", "format", "status_code"],
  };

  const fields = inputFieldsMap[nodeType] || [];
  const defaultValues: Record<string, any> = {};

  fields.forEach((field) => {
    defaultValues[field] = null; // Default to null, can be customized per field type
  });

  return defaultValues;
}

// Helper function to get default output fields for a node type
function getDefaultOutputFields(nodeType: string): Record<string, any> {
  const outputFieldsMap: Record<string, string[]> = {
    start: ["user_input", "session_id", "timestamp"],
    agent: ["response", "confidence", "tokens_used"],
    knowledge: ["search_results", "relevance_score", "sources"],
    condition: ["condition_result", "branch_taken"],
    function: ["function_output", "execution_time", "status"],
    response: [], // Response nodes typically don't have outputs
  };

  const fields = outputFieldsMap[nodeType] || [];
  const defaultValues: Record<string, any> = {};

  fields.forEach((field) => {
    defaultValues[field] = null; // Default to null, can be customized per field type
  });

  return defaultValues;
}

// Node state store interface
interface NodeStateStore {
  // Store all node states by nodeId
  nodeStates: Record<string, NodeState>;

  // Actions for node lifecycle
  createNodeState: (
    nodeId: string,
    nodeType: string,
    initialSettings?: Partial<NodeState["settings"]>,
  ) => void;
  deleteNodeState: (nodeId: string) => void;
  duplicateNodeState: (
    sourceNodeId: string,
    targetNodeId: string,
    targetNodeType?: string,
  ) => void;

  // Actions for node settings
  updateNodeSettings: (
    nodeId: string,
    settings: Partial<NodeState["settings"]>,
  ) => void;
  getNodeSettings: (nodeId: string) => NodeState["settings"] | undefined;

  // Actions for input attributes
  updateInputAttribute: (
    nodeId: string,
    attributeName: string,
    value: any,
  ) => void;
  updateInputAttributes: (
    nodeId: string,
    attributes: Record<string, any>,
  ) => void;
  getInputAttribute: (nodeId: string, attributeName: string) => any;
  getInputAttributes: (nodeId: string) => Record<string, any>;

  // Actions for output attributes
  updateOutputAttribute: (
    nodeId: string,
    attributeName: string,
    value: any,
  ) => void;
  updateOutputAttributes: (
    nodeId: string,
    attributes: Record<string, any>,
  ) => void;
  getOutputAttribute: (nodeId: string, attributeName: string) => any;
  getOutputAttributes: (nodeId: string) => Record<string, any>;

  // Actions for edge management
  addInputEdge: (nodeId: string, edgeId: string) => void;
  addOutputEdge: (nodeId: string, edgeId: string) => void;
  removeInputEdge: (nodeId: string, edgeId: string) => void;
  removeOutputEdge: (nodeId: string, edgeId: string) => void;
  getInputEdges: (nodeId: string) => string[];
  getOutputEdges: (nodeId: string) => string[];

  // Utility actions
  getNodeState: (nodeId: string) => NodeState | undefined;
  hasNodeState: (nodeId: string) => boolean;
  getAllNodeStates: () => Record<string, NodeState>;
  clearAllNodeStates: () => void;
}

// Create the node state store
export const useNodeStateStore = create<NodeStateStore>()(
  // Temporarily disable persistence to debug
  // persist(
  subscribeWithSelector((set, get) => ({
    // Initial state
    nodeStates: {},

    // Node lifecycle actions
    createNodeState: (nodeId, nodeType, initialSettings = {}) => {
      console.log(`Creating node state for ${nodeId}:`, {
        nodeType,
        initialSettings,
      });

      const now = Date.now();
      const defaultSettings = {
        label:
          initialSettings.label ||
          nodeType.charAt(0).toUpperCase() + nodeType.slice(1),
        description: initialSettings.description || `${nodeType} node`,
        enabled: true,
        hasVerticalPorts: false,
        ...initialSettings,
      };

      const newNodeState: NodeState = {
        nodeId,
        nodeType,
        settings: defaultSettings,
        inputAttributes: getDefaultInputFields(nodeType),
        outputAttributes: getDefaultOutputFields(nodeType),
        inputEdges: [],
        outputEdges: [],
        createdAt: now,
        updatedAt: now,
      };

      console.log(`Created node state for ${nodeId}:`, newNodeState);

      set((state) => ({
        nodeStates: {
          ...state.nodeStates,
          [nodeId]: newNodeState,
        },
      }));
    },

    deleteNodeState: (nodeId) => {
      set((state) => {
        const { [nodeId]: deleted, ...remaining } = state.nodeStates;
        return { nodeStates: remaining };
      });
    },

    duplicateNodeState: (sourceNodeId, targetNodeId, targetNodeType) => {
      const sourceState = get().nodeStates[sourceNodeId];
      if (!sourceState) return;

      const now = Date.now();
      const duplicatedState: NodeState = {
        ...JSON.parse(JSON.stringify(sourceState)), // Deep clone
        nodeId: targetNodeId,
        nodeType: targetNodeType || sourceState.nodeType,
        inputEdges: [], // New node starts with no edges
        outputEdges: [], // New node starts with no edges
        createdAt: now,
        updatedAt: now,
      };

      set((state) => ({
        nodeStates: {
          ...state.nodeStates,
          [targetNodeId]: duplicatedState,
        },
      }));
    },

    // Node settings actions
    updateNodeSettings: (nodeId, settings) => {
      console.log(`Updating node settings for ${nodeId}:`, settings);

      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState) {
          console.log(`No node state found for ${nodeId}`);
          return state;
        }

        const updatedNodeState = {
          ...nodeState,
          settings: { ...nodeState.settings, ...settings },
          updatedAt: Date.now(),
        };

        console.log(`Updated node state for ${nodeId}:`, updatedNodeState);

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: updatedNodeState,
          },
        };
      });
    },

    getNodeSettings: (nodeId) => {
      return get().nodeStates[nodeId]?.settings;
    },

    // Input attributes actions
    updateInputAttribute: (nodeId, attributeName, value) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              inputAttributes: {
                ...nodeState.inputAttributes,
                [attributeName]: value,
              },
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    updateInputAttributes: (nodeId, attributes) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              inputAttributes: {
                ...nodeState.inputAttributes,
                ...attributes,
              },
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    getInputAttribute: (nodeId, attributeName) => {
      return get().nodeStates[nodeId]?.inputAttributes[attributeName];
    },

    getInputAttributes: (nodeId) => {
      return get().nodeStates[nodeId]?.inputAttributes || {};
    },

    // Output attributes actions
    updateOutputAttribute: (nodeId, attributeName, value) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              outputAttributes: {
                ...nodeState.outputAttributes,
                [attributeName]: value,
              },
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    updateOutputAttributes: (nodeId, attributes) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              outputAttributes: {
                ...nodeState.outputAttributes,
                ...attributes,
              },
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    getOutputAttribute: (nodeId, attributeName) => {
      return get().nodeStates[nodeId]?.outputAttributes[attributeName];
    },

    getOutputAttributes: (nodeId) => {
      return get().nodeStates[nodeId]?.outputAttributes || {};
    },

    // Edge management actions
    addInputEdge: (nodeId, edgeId) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState || nodeState.inputEdges.includes(edgeId)) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              inputEdges: [...nodeState.inputEdges, edgeId],
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    addOutputEdge: (nodeId, edgeId) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState || nodeState.outputEdges.includes(edgeId)) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              outputEdges: [...nodeState.outputEdges, edgeId],
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    removeInputEdge: (nodeId, edgeId) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              inputEdges: nodeState.inputEdges.filter((id) => id !== edgeId),
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    removeOutputEdge: (nodeId, edgeId) => {
      set((state) => {
        const nodeState = state.nodeStates[nodeId];
        if (!nodeState) return state;

        return {
          nodeStates: {
            ...state.nodeStates,
            [nodeId]: {
              ...nodeState,
              outputEdges: nodeState.outputEdges.filter((id) => id !== edgeId),
              updatedAt: Date.now(),
            },
          },
        };
      });
    },

    getInputEdges: (nodeId) => {
      return get().nodeStates[nodeId]?.inputEdges || [];
    },

    getOutputEdges: (nodeId) => {
      return get().nodeStates[nodeId]?.outputEdges || [];
    },

    // Utility actions
    getNodeState: (nodeId) => {
      return get().nodeStates[nodeId];
    },

    hasNodeState: (nodeId) => {
      return nodeId in get().nodeStates;
    },

    getAllNodeStates: () => {
      return get().nodeStates;
    },

    clearAllNodeStates: () => {
      set({ nodeStates: {} });
    },
  })),
  // Temporarily disable persistence to debug
  // {
  //   name: "shoshin-node-states",
  //   partialize: (state) => ({
  //     nodeStates: state.nodeStates,
  //   }),
  //   // Add version to force reset if needed
  //   version: 1,
  // },
  // ),
);
