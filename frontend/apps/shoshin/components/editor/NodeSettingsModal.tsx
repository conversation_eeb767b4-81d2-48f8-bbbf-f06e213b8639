"use client"

import { X } from "lucide-react"
import { use<PERSON>ffe<PERSON>, useMemo, useState } from "react"
import { getCoreBlock } from "../../blocks/core-blocks"
import { useEditorStore } from "../../stores/editorStore"
import { useNodeStateStore } from "../../stores/nodeStateStore"
import { But<PERSON> } from "../ui/button"
import {
    <PERSON><PERSON>,
    Dialog<PERSON>ontent,
    DialogHeader,
    DialogTitle,
} from "../ui/dialog"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Switch } from "../ui/switch"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../ui/tabs"
import { Textarea } from "../ui/textarea"

// Helper function to get the modal title
function getModalTitle(nodeType: string, userLabel: string): string {
  // If user has provided a label, use it
  if (userLabel && userLabel.trim()) {
    return `Node Settings - ${userLabel.trim()}`
  }

  // Otherwise, use the default name from core blocks
  const coreBlock = getCoreBlock(nodeType)
  const defaultName = coreBlock?.name || nodeType || "Node"
  return `Node Settings - ${defaultName}`
}

// Helper function to get connected nodes
function getConnectedNodes(nodeId: string, nodes: any[], edges: any[]) {
  const previousNodes = edges
    .filter(edge => edge.target === nodeId)
    .map(edge => nodes.find(node => node.id === edge.source))
    .filter(Boolean)

  const nextNodes = edges
    .filter(edge => edge.source === nodeId)
    .map(edge => nodes.find(node => node.id === edge.target))
    .filter(Boolean)

  return { previousNodes, nextNodes }
}

// Helper function to get node output fields based on node type
function getNodeOutputFields(nodeType: string): string[] {
  const outputFieldsMap: Record<string, string[]> = {
    start: ["user_input", "session_id", "timestamp"],
    agent: ["response", "confidence", "tokens_used"],
    knowledge: ["search_results", "relevance_score", "sources"],
    condition: ["condition_result", "branch_taken"],
    function: ["function_output", "execution_time", "status"],
    response: [], // Response nodes typically don't have outputs
  }

  return outputFieldsMap[nodeType] || []
}

// Helper function to get node input fields based on node type
function getNodeInputFields(nodeType: string): string[] {
  const inputFieldsMap: Record<string, string[]> = {
    start: [], // Start nodes typically don't have inputs
    agent: ["prompt", "context", "temperature"],
    knowledge: ["query", "max_results", "threshold"],
    condition: ["input_value", "condition_type", "comparison_value"],
    function: ["function_name", "parameters", "timeout"],
    response: ["response_data", "format", "status_code"],
  }

  return inputFieldsMap[nodeType] || []
}

interface NodeSettingsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  nodeId: string | null
}

export function NodeSettingsModal({
  open,
  onOpenChange,
  nodeId,
}: NodeSettingsModalProps) {
  const { nodes, edges, setNodes } = useEditorStore()
  const {
    getNodeState,
    updateNodeSettings,
    getInputAttributes,
    getOutputAttributes,
    updateInputAttribute,
    updateOutputAttribute
  } = useNodeStateStore()

  // Find the current node
  const currentNode = nodeId ? nodes.find(node => node.id === nodeId) : null
  const nodeState = nodeId ? getNodeState(nodeId) : null

  // Local state for form fields - initialize with empty values, will be set by useEffect
  const [label, setLabel] = useState<string>("")
  const [description, setDescription] = useState<string>("")
  const [enabled, setEnabled] = useState<boolean>(true)
  const [hasVerticalPorts, setHasVerticalPorts] = useState<boolean>(false)
  const [activeTab, setActiveTab] = useState("settings")

  // Local state for input and output attributes
  const [inputAttributeValues, setInputAttributeValues] = useState<Record<string, any>>({})
  const [outputAttributeValues, setOutputAttributeValues] = useState<Record<string, any>>({})

  // Get connected nodes and dynamic fields
  const { previousNodes, nextNodes } = useMemo(() => {
    if (!nodeId) return { previousNodes: [], nextNodes: [] }
    return getConnectedNodes(nodeId, nodes, edges)
  }, [nodeId, nodes, edges])

  // Get input fields (from previous nodes + node's own input fields)
  const inputFields = useMemo(() => {
    if (!currentNode) return []

    const nodeInputFields = getNodeInputFields(String(currentNode.data?.type || ""))
    const previousNodeOutputs = previousNodes.flatMap(node =>
      getNodeOutputFields(String(node.data?.type || ""))
    )

    return [...new Set([...nodeInputFields, ...previousNodeOutputs])]
  }, [currentNode, previousNodes])

  // Get output fields (node's own outputs + next nodes' input fields)
  const outputFields = useMemo(() => {
    if (!currentNode) return []

    const nodeOutputFields = getNodeOutputFields(String(currentNode.data?.type || ""))
    const nextNodeInputs = nextNodes.flatMap(node =>
      getNodeInputFields(String(node.data?.type || ""))
    )

    return [...new Set([...nodeOutputFields, ...nextNodeInputs])]
  }, [currentNode, nextNodes])

  // Update local state when node changes
  useEffect(() => {
    if (nodeState) {
      setLabel(String(nodeState.settings.label || ""))
      setDescription(String(nodeState.settings.description || ""))
      setEnabled(Boolean(nodeState.settings.enabled ?? true))
      setHasVerticalPorts(Boolean(nodeState.settings.hasVerticalPorts ?? false))
      setInputAttributeValues(getInputAttributes(nodeId!))
      setOutputAttributeValues(getOutputAttributes(nodeId!))
    } else if (currentNode) {
      setLabel(String(currentNode.data?.label || ""))
      setDescription(String(currentNode.data?.description || ""))
      setEnabled(Boolean(currentNode.data?.enabled ?? true))
      setHasVerticalPorts(Boolean(currentNode.data?.hasVerticalPorts ?? false))
    }
  }, [nodeId, nodeState, currentNode, getInputAttributes, getOutputAttributes])

  const handleSave = () => {
    if (!currentNode || !nodeId) return

    // Update node settings in nodeStateStore
    updateNodeSettings(nodeId, {
      label,
      description,
      enabled,
      hasVerticalPorts,
    })

    // Update the node in the editor store (for backward compatibility)
    const updatedNodes = nodes.map(node => {
      if (node.id === nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            label,
            description,
            enabled,
            hasVerticalPorts,
          },
        }
      }
      return node
    })

    setNodes(updatedNodes)

    // Save input and output attribute values
    Object.entries(inputAttributeValues).forEach(([key, value]) => {
      updateInputAttribute(nodeId, key, value)
    })
    Object.entries(outputAttributeValues).forEach(([key, value]) => {
      updateOutputAttribute(nodeId, key, value)
    })

    onOpenChange(false)
  }

  const handleCancel = () => {
    // Reset form to original values
    if (nodeState) {
      setLabel(String(nodeState.settings.label || ""))
      setDescription(String(nodeState.settings.description || ""))
      setEnabled(Boolean(nodeState.settings.enabled ?? true))
      setHasVerticalPorts(Boolean(nodeState.settings.hasVerticalPorts ?? false))
      setInputAttributeValues(getInputAttributes(nodeId!))
      setOutputAttributeValues(getOutputAttributes(nodeId!))
    } else if (currentNode) {
      setLabel(String(currentNode.data?.label || ""))
      setDescription(String(currentNode.data?.description || ""))
      setEnabled(Boolean(currentNode.data?.enabled ?? true))
      setHasVerticalPorts(Boolean(currentNode.data?.hasVerticalPorts ?? false))
    }
    onOpenChange(false)
  }

  if (!currentNode) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent key={nodeId} className="flex flex-col gap-0 overflow-hidden p-0 w-[45vw] min-w-[500px] h-[80vh] max-w-none [&>button]:hidden">
        <DialogHeader className="flex-shrink-0 border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-medium">
              {getModalTitle(String(currentNode?.data?.type || ""), String(label))}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0"
              onClick={handleCancel}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-col flex-1 min-h-0 overflow pr-12">
          <TabsList className="grid w-full grid-cols-3 flex-shrink-0 mx-6 mt-4 mb-2 ">
            <TabsTrigger value="settings" className="text-sm">Settings</TabsTrigger>
            <TabsTrigger value="input" className="text-sm">Input</TabsTrigger>
            <TabsTrigger value="output" className="text-sm">Output</TabsTrigger>
          </TabsList>

          <TabsContent value="settings" className="flex-1 overflow-y-auto px-6 -mr-12 py-4 mt-0 min-h-0">
            <div className="space-y-6">
              {/* Basic Settings */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-foreground">Basic Settings</h3>

                <div className="space-y-2">
                  <Label htmlFor="node-label">Label</Label>
                  <Input
                    id="node-label"
                    value={String(label)}
                    onChange={(e) => setLabel(e.target.value)}
                    placeholder="Enter node label"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="node-description">Description</Label>
                  <Textarea
                    id="node-description"
                    value={String(description)}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter node description"
                    rows={3}
                  />
                </div>
              </div>

              {/* Behavior Settings */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-foreground">Behavior</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="node-enabled">Enabled</Label>
                    <p className="text-xs text-muted-foreground">
                      Whether this node is active in the workflow
                    </p>
                  </div>
                  <Switch
                    id="node-enabled"
                    checked={Boolean(enabled)}
                    onCheckedChange={setEnabled}
                  />
                </div>

                <div className="flex items-center justify-between opacity-50">
                  <div className="space-y-0.5">
                    <Label htmlFor="node-ports" className="text-muted-foreground">
                      Vertical Ports
                      <span className="ml-2 text-xs bg-muted px-2 py-0.5 rounded-full">Coming Soon</span>
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Use vertical connection ports instead of horizontal
                    </p>
                  </div>
                  <Switch
                    id="node-ports"
                    checked={Boolean(hasVerticalPorts)}
                    onCheckedChange={() => {}} // Disabled - coming soon
                    disabled={true}
                  />
                </div>
              </div>

              {/* Node Info */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-foreground">Node Information</h3>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label className="text-xs text-muted-foreground">Type</Label>
                    <p className="font-medium">{String(currentNode?.data?.type || "Unknown")}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">ID</Label>
                    <p className="font-mono text-xs">{currentNode.id}</p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="input" className="flex-1 overflow-y-auto px-6 py-4 mt-0 min-h-0 -mr-12">
            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-foreground">Input Configuration</h3>
                <p className="text-sm text-muted-foreground">
                  Configure input parameters and data sources for this node.
                </p>

                {/* Connected Previous Nodes */}
                {previousNodes.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      Connected Input Nodes
                    </h4>
                    <div className="grid gap-2">
                      {previousNodes.map((node) => (
                        <div key={node.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{String(node.data?.label || node.id)}</p>
                            <p className="text-xs text-muted-foreground">{String(node.data?.type || "Unknown")}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input Fields */}
                {inputFields.length > 0 ? (
                  <div className="space-y-3">
                    <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      Available Input Fields
                    </h4>
                    <div className="grid gap-3">
                      {inputFields.map((field) => (
                        <div key={field} className="space-y-2">
                          <Label className="text-sm font-medium">{field}</Label>
                          <Input
                            value={inputAttributeValues[field] || ""}
                            onChange={(e) => setInputAttributeValues(prev => ({
                              ...prev,
                              [field]: e.target.value
                            }))}
                            placeholder={`Configure ${field}...`}
                            className="text-sm"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="border border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                    <p className="text-sm text-muted-foreground">
                      {previousNodes.length === 0
                        ? "No input connections. Connect nodes to see input fields."
                        : "No input fields available for this node type."}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="output" className="flex-1 overflow-y-auto px-6 py-4 mt-0 min-h-0 -mr-12">
            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-foreground">Output Configuration</h3>
                <p className="text-sm text-muted-foreground">
                  Configure output parameters and data destinations for this node.
                </p>

                {/* Connected Next Nodes */}
                {nextNodes.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      Connected Output Nodes
                    </h4>
                    <div className="grid gap-2">
                      {nextNodes.map((node) => (
                        <div key={node.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{String(node.data?.label || node.id)}</p>
                            <p className="text-xs text-muted-foreground">{String(node.data?.type || "Unknown")}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Output Fields */}
                {outputFields.length > 0 ? (
                  <div className="space-y-3">
                    <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      Available Output Fields
                    </h4>
                    <div className="grid gap-3">
                      {outputFields.map((field) => (
                        <div key={field} className="space-y-2">
                          <Label className="text-sm font-medium">{field}</Label>
                          <Input
                            value={outputAttributeValues[field] || ""}
                            onChange={(e) => setOutputAttributeValues(prev => ({
                              ...prev,
                              [field]: e.target.value
                            }))}
                            placeholder={`Configure ${field}...`}
                            className="text-sm"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="border border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                    <p className="text-sm text-muted-foreground">
                      {nextNodes.length === 0
                        ? "No output connections. Connect nodes to see output fields."
                        : "No output fields available for this node type."}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <div className="flex-shrink-0 border-t px-6 py-4">
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
